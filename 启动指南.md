# MystiBox 启动指南

## 🚀 快速启动（推荐）

### 方法一：使用一键启动脚本

#### Windows 用户
1. 双击运行 `一键启动.bat` 文件
2. 脚本会自动完成所有配置和启动步骤
3. 等待服务启动完成后，浏览器访问 http://localhost:3000

#### macOS/Linux 用户
1. 打开终端，进入项目目录
2. 运行启动脚本：
   ```bash
   ./一键启动.sh
   ```
3. 等待服务启动完成后，浏览器访问 http://localhost:3000

### 脚本功能说明
一键启动脚本会自动执行以下操作：
- ✅ 检查 Node.js 和 npm 环境
- ✅ 安装前端和后端依赖
- ✅ 创建环境配置文件
- ✅ 初始化数据库和测试数据
- ✅ 同时启动前后端服务
- ✅ 提供详细的状态反馈

### 方法二：使用简易启动脚本（如果一键启动失败）

如果一键启动脚本遇到问题，可以使用简易版本：

#### Windows 用户
```bash
# 双击运行
简易启动.bat
```

#### macOS/Linux 用户
```bash
# 在终端中运行
./简易启动.sh
```

**注意**：简易启动脚本假设您已经安装了依赖和初始化了数据库。如果是首次运行，请先使用一键启动脚本或手动安装依赖。

## 📋 环境要求

在使用一键启动脚本之前，请确保您的系统满足以下要求：

### 必需软件
- **Node.js** >= 16.0.0
- **npm** >= 8.0.0 (通常随 Node.js 一起安装)

### 安装 Node.js
如果您还没有安装 Node.js，请按照以下步骤：

1. 访问 [Node.js 官网](https://nodejs.org/)
2. 下载 LTS 版本（推荐）
3. 运行安装程序并按照提示完成安装
4. 验证安装：
   ```bash
   node --version
   npm --version
   ```

## 🛠️ 手动启动（备选方案）

如果一键启动脚本遇到问题，您可以按照以下步骤手动启动：

### 步骤 1：安装依赖

```bash
# 安装前端依赖
npm install

# 安装后端依赖
cd backend
npm install
cd ..
```

### 步骤 2：配置环境变量

在 `backend` 目录下创建 `.env` 文件：

```bash
cd backend
cp .env.example .env
```

编辑 `.env` 文件内容：
```env
# 数据库配置
DATABASE_URL="file:./dev.db"

# JWT密钥 (请更改为随机字符串)
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"

# 服务端口
PORT=3003

# 前端URL (用于CORS)
FRONTEND_URL="http://localhost:3000"
```

### 步骤 3：初始化数据库

```bash
cd backend

# 生成 Prisma 客户端
npx prisma generate

# 创建数据库表
npx prisma db push

# 填充测试数据（可选）
node prisma/seed.js

cd ..
```

### 步骤 4：启动服务

#### 方式一：分别启动（推荐用于开发）

**终端 1 - 启动后端：**
```bash
cd backend
npm start
# 后端将在 http://localhost:3003 启动
```

**终端 2 - 启动前端：**
```bash
npm run dev
# 前端将在 http://localhost:3000 启动
```

#### 方式二：并发启动（如果配置了并发脚本）

```bash
npm run dev:all
```

### 步骤 5：访问应用

打开浏览器访问：**http://localhost:3000**

## 🔧 故障排除

### 常见问题及解决方案

#### 1. 端口被占用
**错误信息**：`Error: listen EADDRINUSE: address already in use :::3000`

**解决方案**：
```bash
# 查看端口占用
# Windows
netstat -ano | findstr :3000
netstat -ano | findstr :3003

# macOS/Linux
lsof -i :3000
lsof -i :3003

# 杀死占用进程
# Windows
taskkill /PID <PID> /F

# macOS/Linux
kill -9 <PID>
```

#### 2. 依赖安装失败
**解决方案**：
```bash
# 清除缓存
npm cache clean --force

# 删除 node_modules 重新安装
rm -rf node_modules package-lock.json
npm install

# 后端也执行相同操作
cd backend
rm -rf node_modules package-lock.json
npm install
```

#### 3. 数据库问题
**解决方案**：
```bash
cd backend

# 重置数据库
npx prisma db push --force-reset

# 重新填充数据
node prisma/seed.js
```

#### 4. 权限问题（macOS/Linux）
**解决方案**：
```bash
# 给脚本执行权限
chmod +x 一键启动.sh

# 如果仍有问题，尝试使用 sudo
sudo ./一键启动.sh
```

#### 5. Node.js 版本过低
**解决方案**：
- 升级到 Node.js 16.0.0 或更高版本
- 使用 nvm 管理多个 Node.js 版本：
  ```bash
  # 安装 nvm
  curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
  
  # 安装并使用 Node.js 18
  nvm install 18
  nvm use 18
  ```

## 📱 访问应用

### 主要页面
- **首页**：http://localhost:3000
- **盲盒商店**：http://localhost:3000/store
- **我的宠物**：http://localhost:3000/my-pets
- **宠物广场**：http://localhost:3000/plaza
- **个人中心**：http://localhost:3000/account
- **管理后台**：http://localhost:3000/admin

### 测试账户
- **管理员账户**：
  - 用户名：`admin`
  - 密码：`admin123456`

- **普通用户账户**：
  - 用户名：`demo`
  - 密码：`demo123`

## 🔍 开发工具

### 数据库管理
```bash
cd backend
npx prisma studio
# 在浏览器中打开数据库管理界面
```

### API 测试
- 后端健康检查：http://localhost:3003/health
- API 文档：http://localhost:3003/api/v1/

### 日志查看
- 前端：浏览器开发者工具 Console
- 后端：终端输出日志

## 📞 技术支持

如果您在启动过程中遇到问题：

1. **检查环境要求**：确保 Node.js 版本 >= 16.0.0
2. **查看错误日志**：注意终端中的错误信息
3. **重启服务**：按 Ctrl+C 停止服务后重新启动
4. **清除缓存**：删除 node_modules 重新安装依赖
5. **检查端口**：确保 3000 和 3003 端口未被占用

## 🎯 下一步

启动成功后，您可以：
1. 注册新账户或使用测试账户登录
2. 浏览盲盒商店，体验抽卡功能
3. 收集和培养宠物
4. 在广场分享您的宠物
5. 添加好友并进行社交互动

---

**祝您使用愉快！** 🎮✨
