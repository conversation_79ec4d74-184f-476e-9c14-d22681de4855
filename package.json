{"name": "mystibox", "version": "1.0.0", "description": "MystiBox - 神秘宠物盲盒收藏游戏", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:check": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"axios": "^1.6.2", "pinia": "^2.1.7", "vite": "^7.0.6", "vue": "^3.4.0", "vue-router": "^4.2.5"}, "devDependencies": {"@types/node": "^20.10.4", "@vitejs/plugin-vue": "^4.0.5", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-vue": "^9.19.2", "postcss": "^8.4.32", "prettier": "^3.1.1", "tailwindcss": "^3.3.6", "typescript": "^5.2.2", "vue-tsc": "^1.8.25"}}